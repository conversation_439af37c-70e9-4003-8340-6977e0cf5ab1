#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <sstream>
#include <chrono>
#include <thread>
#include <cstring>
#include <cassert>

// 测试结果结构体 - 必须在mock函数之前定义
struct TestResult {
    int orderReceived;
    int cancelReceived;
    int tickCount;
    std::string orderId;
};

// 全局测试结果
TestResult g_testResult;

// 定义测试模式宏，防止 simple_strategy.cc 中的 main 函数被编译
#define STRATEGY_TEST_MODE

// 在包含任何头文件之前定义宏来重定向DLL导入符号
#define strategy_log mock_strategy_log
#define td_order mock_td_order
#define td_cancel_order mock_td_cancel_order
#define hft_strerror mock_hft_strerror
#define hft_strerror_utf8 mock_hft_strerror_utf8
#define get_security_ticks mock_get_security_ticks
#define get_security_kdata mock_get_security_kdata
#define strategy_init mock_strategy_init
#define strategy_exit mock_strategy_exit
#define strategy_exit_reason mock_strategy_exit_reason
#define strategy_report_indexes mock_strategy_report_indexes

#include "md_api.h"
#include "strategy_api.h"
#include "trade_api.h"
#include "md_def.h"
#include "trade_def.h"
// #include "strategy_test.h"

// 定义mock函数
extern "C" {
    void mock_strategy_log(StrategyLogLevel level, const char* message, bool is_gbk) {
        const char* level_str = "UNKNOWN";
        switch(level) {
            case StrategyLogLevel_Debug: level_str = "DEBUG"; break;
            case StrategyLogLevel_Info: level_str = "INFO"; break;
            case StrategyLogLevel_Warn: level_str = "WARN"; break;
            case StrategyLogLevel_Error: level_str = "ERROR"; break;
        }
        std::cout << "[MOCK LOG " << level_str << "] " << message << std::endl;
    }

    int mock_strategy_init(const char* config_dir, const char* log_dir) {
        std::cout << "[MOCK] strategy_init called" << std::endl;
        return 0;
    }

    int mock_strategy_exit() {
        std::cout << "[MOCK] strategy_exit called" << std::endl;
        return 0;
    }

    const char* mock_strategy_exit_reason(int reason) {
        return "Mock exit reason";
    }

    int mock_strategy_report_indexes(const char* indexes_json) {
        std::cout << "[MOCK] strategy_report_indexes: " << indexes_json << std::endl;
        return 0;
    }

    int mock_td_order(const char* account_id, AccountType account_type, OrderReq* orders, int reqnum, int async) {

        // 模拟成功下单并直接更新测试结果
        for (int i = 0; i < reqnum; i++) {
            if (orders[i].price == 90200) continue;
            // 直接更新测试结果
            g_testResult.orderReceived += 1;
            g_testResult.orderId = orders[i].order_id;
        }
        return 0;
    }

    int mock_td_cancel_order(const char* account_id, AccountType account_type, const char* order_ids,
                            CancelDetail** cancel_list, int* count) {
        // 直接更新测试结果
        g_testResult.cancelReceived += 1;

        // 模拟撤单成功
        if (cancel_list && count) {
            *count = 0;
            *cancel_list = nullptr;
        }

        return 0;
    }

    const char* mock_hft_strerror(int err) {
        static char error_msg[256];
        snprintf(error_msg, sizeof(error_msg), "[MOCK] Error code: %d", err);
        return error_msg;
    }

    const char* mock_hft_strerror_utf8(int err) {
        return mock_hft_strerror(err);
    }

    int mock_get_security_ticks(const char* symbol_list, const char* begin_time, const char* end_time,
                               SecurityTickData** std, int* count) {
        std::cout << "[MOCK] get_security_ticks called - symbols: " << symbol_list << std::endl;

        if (std && count) {
            *count = 0;
            *std = nullptr;
        }
        return 0;
    }

    int mock_get_security_kdata(const char* symbol_list, const char* begin_date, const char* end_date,
                               const char* frequency, const char* fq, SecurityKdata** skd, int* count) {
        std::cout << "[MOCK] get_security_kdata called - symbols: " << symbol_list << std::endl;

        if (skd && count) {
            *count = 0;
            *skd = nullptr;
        }
        return 0;
    }
}

// 现在包含simple_strategy.cc，它会使用上面定义的mock函数
#include "simple_strategy.cc"

// 添加板块监控测试的辅助函数
bool LoadSectorConfig(const std::string& config_file) {
    std::ifstream file(config_file);
    if (!file.is_open()) {
        std::cerr << "Cannot open sector config file: " << config_file << std::endl;
        return false;
    }

    std::string content((std::istreambuf_iterator<char>(file)),
                        std::istreambuf_iterator<char>());
    file.close();

    try {
        json config = json::parse(content);

        for (const auto& item : config) {
            if (item.contains("key") && item["key"] == "SectorGroup" &&
                item.contains("value")) {
                const json& sector_data = item["value"];

                if (sector_data.contains("group_name") && sector_data.contains("codes")) {
                    std::string group_name = sector_data["group_name"];
                    const json& codes = sector_data["codes"];

                    if (AddSectorGroup(group_name.c_str(), codes)) {
                        std::cout << "Added sector group: " << group_name
                                  << " with " << codes.size() << " stocks" << std::endl;
                    }
                }
            }
        }
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error parsing sector config: " << e.what() << std::endl;
        return false;
    }
}


#include <map>

// 测试用例数据结构
struct TestCase {
    std::string description;
    std::string dataFilePath;
    int expectOrder;
    int expectCancel;
};



// 模拟的行情数据
std::vector<std::string> g_marketDataLines;
int g_currentLine = 0;

// 解析行情数据行
bool parseMarketDataLine(const std::string& line, TickByTickData* tbt, SecurityTickData* tick) {
    // 示例格式: [100001]|SH.600002|TICK|V:100000|T:93000000|N:1000001|M:10000|TAV:150000|BP1:10000|BV1:100000

    // 或者: [377639]|SH.605255|BW|V:29800|T:93713190|N:2395706
    
    std::istringstream iss(line);
    std::string timestamp, symbol, type;
    std::map<std::string, int64_t> fields;
    
    char buffer[1024];
    strncpy(buffer, line.c_str(), sizeof(buffer));
    buffer[sizeof(buffer) - 1] = '\0';
    
    char* token = strtok(buffer, "|");
    if (!token) return false;
    timestamp = token;
    
    token = strtok(nullptr, "|");
    if (!token) return false;
    symbol = token;
    
    token = strtok(nullptr, "|");
    if (!token) return false;
    type = token;
    
    // 解析所有剩余字段
    while ((token = strtok(nullptr, "|")) != nullptr) {
        std::string field = token;
        size_t pos = field.find(':');
        if (pos != std::string::npos) {
            std::string key = field.substr(0, pos);
            std::string value = field.substr(pos + 1);
            int64_t numValue = 0;
            sscanf(value.c_str(), "%lld", &numValue);
            fields[key] = numValue;
        }
    }
    
    // 提取时间戳
    int64_t timestampValue = 0;
    sscanf(timestamp.c_str() + 1, "%lld", &timestampValue);
    
    // 根据类型填充不同的数据结构
    if (type == "TICK") {
        // 填充Tick数据 - 从字段映射中获取值
        memset(tick, 0, sizeof(SecurityTickData));
        strcpy(tick->symbol, symbol.c_str());
        
        // 必须存在的字段
        tick->time = fields["T"];
        tick->volume = fields["V"];
        
        // 从字段映射中获取值，如果不存在则使用默认值
        tick->match = fields.count("M") ? fields["M"] : 108000;

        
        // 设置基本字段
        tick->open = tick->match; // 使用最新价作为开盘价

        // 根据股票类型设置不同的涨跌停价
        if (is_10cm_limit_stock(tick->symbol)) {
            tick->high_limited = 110000; // 10cm股票涨停价
            tick->low_limited = 90000;   // 10cm股票跌停价
        } else {
            tick->high_limited = 120000; // 20cm股票涨停价
            tick->low_limited = 80000;   // 20cm股票跌停价
        }

        tick->pre_close = fields.count("PC") ? fields["PC"] : 100000;    // 前收盘价，用于7%涨幅计算

        // 设置买卖盘数据
        tick->bid_vol[0] = fields.count("BV1") ? fields["BV1"] : 100000;


        // 设置总委买卖量
        tick->total_ask_vol = fields.count("TAV") ? fields["TAV"] : fields["V"];
        
        // 设置成交相关数据 - 如果没有提供，则根据成交量和价格计算
        tick->turnover = fields.count("TO") ? fields["TO"] : 100000000;
        
        return true;
    } 
    else if (type == "BW" || type == "CW" || type == "SW") {
        // 填充逐笔数据
        memset(tbt, 0, sizeof(TickByTickData));
        strcpy(tbt->symbol, symbol.c_str());
        tbt->data_time = fields["T"];
        
        if (type == "BW") {
            // 买入委托
            tbt->type = '0'; // 委托
            tbt->data.entrust.side = '1'; // 买入
            tbt->data.entrust.ord_type = 'A'; // 增加
            tbt->data.entrust.qty = fields["V"];

            // 根据股票类型设置不同的涨停价委托
            if (is_10cm_limit_stock(tbt->symbol)) {
                tbt->data.entrust.price = 110000; // 10cm股票涨停价委托
            } else {
                tbt->data.entrust.price = 120000; // 20cm股票涨停价委托
            }
        }
        else if (type == "CW") {
            // 撤单委托
            tbt->type = '0'; // 委托
            tbt->data.entrust.side = '1'; // 买入
            tbt->data.entrust.ord_type = 'D'; // 删除
            tbt->data.entrust.price = 110000; // 涨停价
            tbt->data.entrust.qty = fields["V"];
        }
        else if (type == "SW") {
            // 卖出委托
            tbt->type = '0'; // 委托
            tbt->data.entrust.side = '2'; // 卖出
            tbt->data.entrust.ord_type = 'A'; // 增加
            tbt->data.entrust.price = 110000; // 涨停价
            tbt->data.entrust.qty = fields["V"];
        }
        
        return true;
    }
    
    return false;
}



// 从文件加载测试数据
bool loadTestData(const std::string& filePath) {
    std::ifstream file(filePath);
    if (!file.is_open()) {
        std::cerr << "Cannot open file: " << filePath << std::endl;
        return false;
    }
    
    g_marketDataLines.clear();
    std::string line;
    while (std::getline(file, line)) {
        if (!line.empty()) {
            g_marketDataLines.push_back(line);
        }
    }
    
    file.close();
    g_currentLine = 0;
    return !g_marketDataLines.empty();
}

// 运行单个测试用例
bool runTestCase(const TestCase& testCase) {
    std::cout << "Starting test: " << testCase.description << std::endl;

    // 重置测试结果
    g_testResult.orderReceived = 0;
    g_testResult.cancelReceived = 0;
    g_testResult.tickCount = 0;
    g_testResult.orderId = "";

    // 重置板块分组状态（确保测试用例之间的独立性）
    for (int i = 0; i < g_sector_group_count; ++i) {
        g_sector_groups[i].has_ordered = false;
    }

    // 重置订阅状态
    g_subscription_count = 0;
    memset(g_limitup_subscriptions, 0, sizeof(g_limitup_subscriptions));
    memset(g_sz_realtime_data, 0, sizeof(g_sz_realtime_data));

    // 如果是板块监控测试，加载板块配置
    if (testCase.description.find("板块监控") != std::string::npos) {
        if (!LoadSectorConfig("test_sector_config.json")) {
            std::cerr << "Failed to load sector config" << std::endl;
            return false;
        }
        std::cout << "Loaded sector config for monitoring test" << std::endl;
    }

    // 如果是涨跌停幅度分组测试，加载混合配置
    if (testCase.description.find("涨跌停幅度分组") != std::string::npos) {
        if (!LoadSectorConfig("test_limit_group_config.json")) {
            std::cerr << "Failed to load limit group config" << std::endl;
            return false;
        }
        std::cout << "Loaded limit group config for testing" << std::endl;
    }

    // 如果是涨幅阈值测试，加载测试配置
    if (testCase.description.find("涨幅阈值") != std::string::npos) {
        if (!LoadSectorConfig("test_gain_threshold_config.json")) {
            std::cerr << "Failed to load gain threshold config" << std::endl;
            return false;
        }
        std::cout << "Loaded gain threshold config for testing" << std::endl;
    }

    // 加载测试数据
    if (!loadTestData(testCase.dataFilePath)) {
        std::cerr << "Failed to load test data" << std::endl;
        return false;
    }
    
    // 设置回调
    
    // 处理每一行数据
    for (const auto& line : g_marketDataLines) {
        TickByTickData tbt;
        SecurityTickData tick;
        
        if (parseMarketDataLine(line, &tbt, &tick)) {
            // 根据类型调用不同的回调
            if (line.find("TICK") != std::string::npos) {
                OnSecurityTick(&tick, nullptr);

                // 如果是板块监控或涨跌停幅度分组或涨幅阈值测试，在每个tick后调用板块监控定时任务
                if (testCase.description.find("板块监控") != std::string::npos ||
                    testCase.description.find("涨跌停幅度分组") != std::string::npos ||
                    testCase.description.find("涨幅阈值") != std::string::npos) {
                    do_block_monitor();
                }
            } else {
                OnTickbytickData(&tbt, nullptr);
            }
            
            g_testResult.tickCount++;
        }
    }
    
    // 验证测试结果
    bool success = true;
    if (testCase.expectOrder != g_testResult.orderReceived) {
        std::cerr << "Test failed: Expected " << testCase.expectOrder
                  << " orders but got " << g_testResult.orderReceived << std::endl;
        success = false;
    }
    
    if (testCase.expectCancel != g_testResult.cancelReceived) {
        std::cerr << "Test failed: Expected " << testCase.expectCancel
                  << " cancels but got " << g_testResult.cancelReceived << std::endl;
        success = false;
    }
    
    std::cout << "Test " << (success ? "passed" : "failed") << ": " << testCase.description << std::endl;
    return success;
}

// 创建测试数据文件
bool createTestDataFile(const std::string& filePath, const std::vector<std::string>& lines) {
    std::ofstream file(filePath);
    if (!file.is_open()) {
        std::cerr << "无法创建文件: " << filePath << std::endl;
        return false;
    }
    
    for (const auto& line : lines) {
        file << line << std::endl;
    }
    
    file.close();
    return true;
}




// 主函数
int main() {

    
    // 初始化订单请求
    init_order_req();

    // 订阅涨停检测 - 使用测试用的symbol
    const char* test_symbol = "SH.600002";  // 与测试数据匹配
    SubscribeLimitUpDetection(test_symbol, 0);  // 订阅从9:30开始
    std::cout << "Subscribed limit up detection for: " << test_symbol << std::endl;

    // Define test cases
    std::vector<TestCase> testCases = {
        // {"Scenario 1: order and fast cancel", "test_fast_cancel.txt", 1, 1, },  // 期望下单和撤单
        // {"Scenario 2: 931之后直接下单", "test_case2.txt", 1, 0, },
        // {"Scenario 3: 板块监控测试", "test_sector_monitor.txt", 1, 0, },  // 期望只有第一只股票下单
        {"Scenario 4: 涨跌停幅度分组测试", "test_limit_group_monitor.txt", 2, 0, },  // 期望10cm和20cm分组各下单一只
        // {"Scenario 6: Cancel After 4 Ticks", "test_case6.txt", true, true, }
    };
    
    // 运行测试用例
    int passCount = 0;
    for (const auto& testCase : testCases) {
        if (runTestCase(testCase)) {
            passCount++;
        }
    }
    
    std::cout << "Test completed: " << passCount << "/" << testCases.size() << " passed" << std::endl;
    
    // 退出策略
    strategy_exit();
    
    return (passCount == testCases.size()) ? 0 : 1;
} 