# F4 高频交易策略系统 - 开发日志

## 2025-08-01 - 涨跌停幅度分组功能完成 🚀

### 功能修正
#### 涨幅阈值修正
- **20cm股票阈值**：从6%修正为15%涨幅才触发位图标记
- **北交所排除**：明确不监听北交所股票（30cm涨跌停）
- **阈值逻辑**：10cm股票6%涨幅，20cm股票15%涨幅，北交所不监听

### 新增功能
#### 涨跌停幅度自动分组
- **功能描述**：根据股票涨跌停幅度自动分为10cm和20cm两个独立分组
- **股票识别**：10cm（主板）、20cm（创业板300、科创板688、北交所8/4开头）
- **分组逻辑**：一个配置自动分为两组，如"新能源汽车" → "新能源汽车_10CM" + "新能源汽车_20CM"
- **下单规则**：10cm和20cm分组可以同时下单，各自分组内互斥

#### 技术实现
```cpp
enum LimitType { LIMIT_10CM = 0, LIMIT_20CM = 1 };
struct SectorGroup { LimitType limit_type; /* 其他字段 */ };
```
- 修改 `AddSectorGroup` 函数，自动创建两个分组
- 修改 `do_block_monitor` 函数，只订阅匹配类型的股票
- 新增股票类型判断函数

### 测试验证
- **分组测试**：`test_limit_group_config.json`、`test_limit_group_monitor.txt`
- **阈值测试**：`test_gain_threshold_config.json`、`test_gain_threshold.txt`
- **测试结果**：
  - 10cm股票6%阈值测试通过（SH.600104, SZ.002594）
  - 20cm股票15%阈值测试通过（SZ.300750, SH.688981, SZ.300661）
  - 北交所股票正确排除（BJ.830001）
  - 边界值测试通过（6.0%和15.0%恰好触发）

---

## 2025-07-31 - 板块监控系统完成 🎉

### 新增功能
#### 板块监控系统
- **功能描述**：实现板块内股票的智能发现和互斥下单
- **涨幅位图**：使用位图存储涨幅超过6%的股票，支持100万只股票
- **自动订阅**：定时任务 `do_block_monitor` 自动发现并订阅板块内强势股票
- **互斥下单**：确保每个板块只下单一只股票

#### 技术实现
```cpp
struct SectorGroup {
    char group_name[32];
    char codes[MAX_CODES_PER_GROUP][12];
    bool has_ordered;  // 板块内互斥标记
};
static uint64_t g_gain_stock_bitmap[BITMAP_WORDS];  // 涨幅位图
```
- 在 `OnSecurityTick` 中设置涨幅位图标记
- `do_block_monitor` 扫描位图并自动订阅符合条件的股票
- 通过 `sector_group_index` 关联实现板块内互斥

### 测试验证
- **测试文件**：`test_sector_config.json`、`test_sector_monitor.txt`
- **测试结果**：SH.600104成功下单，SH.600066被板块互斥逻辑排除

### 技术修复
- 修复ARM64编译问题，适配Apple Silicon
- 添加测试模式宏避免main函数冲突

---

## 开发规范

### 提交格式
```
日期 - 功能描述 + 状态标识

### 新增功能
- 功能点1
- 功能点2

### 技术优化  
- 优化点1
- 优化点2

### 问题修复
- 修复点1
- 修复点2
```

### 状态标识
- 🎉 重大功能完成
- 🚀 新功能开发
- 🛠️ 技术优化
- 🐛 问题修复
- 📝 文档更新
- 🧪 测试完善
- 📈 性能提升
- 🔮 计划功能
