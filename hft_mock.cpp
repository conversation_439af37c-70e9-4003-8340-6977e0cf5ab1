#include <iostream>
#include <cstring>
#include <cstdio>

// Include the headers to get the correct type definitions
#include "md_api.h"
#include "strategy_api.h"
#include "trade_api.h"

// Mock implementations with correct signatures
extern "C" {

// Strategy API functions
void strategy_log(StrategyLogLevel level, const char* message, bool is_gbk) {
    const char* level_str = "UNKNOWN";
    switch(level) {
        case StrategyLogLevel_Debug: level_str = "DEBUG"; break;
        case StrategyLogLevel_Info: level_str = "INFO"; break;
        case StrategyLogLevel_Warn: level_str = "WARN"; break;
        case StrategyLogLevel_Error: level_str = "ERROR"; break;
    }
    std::cout << "[MOCK LOG " << level_str << "] " << message << std::endl;
}

int strategy_init(const char* config_dir, const char* log_dir) {
    std::cout << "[MOCK] strategy_init called" << std::endl;
    return 0;
}

int strategy_exit() {
    std::cout << "[MOCK] strategy_exit called" << std::endl;
    return 0;
}

const char* strategy_exit_reason(int reason) {
    return "Mock exit reason";
}

int strategy_report_indexes(const char* indexes_json) {
    std::cout << "[MOCK] strategy_report_indexes: " << indexes_json << std::endl;
    return 0;
}

// Common API functions
const char* hft_strerror(int err) {
    static char error_msg[256];
    snprintf(error_msg, sizeof(error_msg), "[MOCK] Error code: %d", err);
    return error_msg;
}

const char* hft_strerror_utf8(int err) {
    return hft_strerror(err);
}

// Market Data API functions
int get_security_ticks(const char* symbol_list, const char* begin_time, const char* end_time, 
                       SecurityTickData** std, int* count) {
    std::cout << "[MOCK] get_security_ticks called - symbols: " << symbol_list << std::endl;
    
    if (std && count) {
        *count = 0;
        *std = nullptr;
    }
    return 0;
}

int get_security_kdata(const char* symbol_list, const char* begin_date, const char* end_date, 
                       const char* frequency, const char* fq, SecurityKdata** skd, int* count) {
    std::cout << "[MOCK] get_security_kdata called - symbols: " << symbol_list << std::endl;
    
    if (skd && count) {
        *count = 0;
        *skd = nullptr;
    }
    return 0;
}

} // extern "C"
